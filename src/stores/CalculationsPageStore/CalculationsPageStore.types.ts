// Описание данных по станции
export interface ICalculationSummary {
  plantId: number // Идентификатор станции
  plantType: 'GES' | 'GAES' // Тип станции
  planTitle: string // Название станции
  plantOptimized: boolean // Признак оптимизации
  accepted: boolean // Признак акцепта
  showInAvrchm: boolean // Флаг необходимости отображения станции в таблице АВРЧМ
  minConsumptionHour: number // Минимальный час потребления
  maxConsumptionHour: number // Максимальный час потребления
  inputValues: {
    [key: string]: TypedParameterObject // Входные параметры для расчёта
  }
  rows: IPlantRow[] // Строки со значениями
  rgus: IRgu[] // Данные по РГЕ станции
  allowedZones: IHourAllowedZone[] // Списки допустимых зон по часам
}

// Описание входных параметров с типами
export interface TypedParameterObject {
  type:
    | 'BOOLEAN'
    | 'TOGGLEABLE'
    | 'INTEGER'
    | 'TOGGLEABLE_INTEGER'
    | 'REGISTRY_TYPE'
    | 'BIGDECIMAL'
    | 'TOGGLEABLE_BIGDECIMAL'
    | 'TOGGLEABLE_GENERATOR_GROUPS'
    | 'TOGGLEABLE_PRESSURE_RECESSION'
    | 'TOGGLEABLE_RESTRICTION_LIMIT'
    | 'TOGGLEABLE_ENERGY_DISTRICT'
    | 'TOGGLEABLE_LOAD_UNLOAD_SPEED'
  value: unknown // Значение параметра
}

// Строки со значениями для расчёта станции
export interface IPlantRow {
  hour: number // Номер часа
  rowName: string // Обозначение строки в таблице
  cells: PlantCalculationRowCellDto[] // Ячейки со значениями
}

// Ячейки со значениями для расчёта станции
export interface PlantCalculationRowCellDto {
  column:
    | 'P_GEN'
    | 'P_SOURCE'
    | 'AVRCHM_LOAD'
    | 'AVRCHM_UNLOAD'
    | 'NPRCH'
    | 'CM_P_MIN'
    | 'CM_P_MAX'
    | 'MODES_P_MIN'
    | 'MODES_P_MAX'
    | 'MODES_DECLARED'
    | 'CONSUMPT'
    | 'RESULT_MIN'
    | 'RESULT_MAX' // Обозначение столбца
  hour: number // Номер часа
  value: number // Значение
  editable: boolean // Редактируемое поле?
}

// Данные по РГЕ станции
export interface IRgu {
  rguId: number // Идентификатор РГЕ
  rguName: string // Название РГЕ
  rows: IRguRow[] // Строки со значениями
  allowedZones: IHourAllowedZone[] // Списки допустимых зон по часам
}

// Строки со значениями для расчёта РГЕ станции
export interface IRguRow {
  hour: number // Номер часа
  rowName: string // Обозначение строки в таблице
  cells: RguRowCell[] // Ячейки со значениями
}

// Ячейки со значениями для расчёта РГЕ станции
export interface RguRowCell {
  column:
    | 'P_GEN'
    | 'AVRCHM_LOAD'
    | 'AVRCHM_UNLOAD'
    | 'NPRCH'
    | 'CM_P_MIN'
    | 'CM_P_MAX'
    | 'MODES_P_MIN'
    | 'MODES_P_MAX'
    | 'MODES_DECLARED' // Обозначение столбца
  hour: number // Номер часа
  value: number // Значение
  editable: boolean // Редактируемое поле?
}

// Списки допустимых зон по часам
export interface IHourAllowedZone {
  hour: number // Номер часа
  zones: IAllowedZone[] // Список допустимых зон
}

// Список допустимых зон
export interface IAllowedZone {
  bottomLine: number // Нижнее значение разрешённой зоны
  topLine: number // Верхнее значение разрешённой зоны
}
