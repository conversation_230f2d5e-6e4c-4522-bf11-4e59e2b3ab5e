import { IHeaderClassesConfigItem } from 'widgets/Spreadsheet'

const classNamesOptimised: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean' && columnData.plantOptimized) {
      result = 'plantOptimized'
      break
    }
  }

  return result
}

const classNamesNotOptimised: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean' && !columnData.plantOptimized) {
      result = 'plantNotOptimized'
      break
    }
  }

  return result
}

const classNamesOptimisedRgu: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean' && columnData.plantOptimized) {
      result = 'plantOptimizedRgu'
      break
    }
  }

  return result
}

const classNamesNotOptimisedRgu: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean' && !columnData.plantOptimized) {
      result = 'plantNotOptimizedRgu'
      break
    }
  }

  return result
}

export const classNamesCommonOptimized: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean') {
      if (columnData.plantOptimized) {
        result = 'plantOptimized'
        break
      } else {
        result = 'plantNotOptimized'
        break
      }
    }
  }

  return result
}

const classNamesCommonOptimizedRgu: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (typeof columnData.plantOptimized === 'boolean') {
      if (columnData.plantOptimized) {
        result = 'plantOptimizedRgu'
        break
      } else {
        result = 'plantNotOptimizedRgu'
        break
      }
    }
  }

  return result
}

export const classNamesValidator: Exclude<IHeaderClassesConfigItem['classNames'], string> = (columnsData) => {
  let result = ''
  for (const columnData of columnsData) {
    if (columnData.hasError) {
      result = 'isNotValid'
      break
    }
  }

  return result
}

export const rguStationHeaderClassNameConfig: IHeaderClassesConfigItem[] = [
  {
    key: /.*/,
    classNames: classNamesValidator,
    children: [
      {
        key: /.*/,
        classNames: classNamesValidator,
        children: [
          {
            key: /.*/,
            classNames: classNamesValidator,
            children: [
              {
                key: /.*/,
                classNames: classNamesValidator,
                children: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    key: /^Итог$/,
    classNames: classNamesCommonOptimized,
    children: [
      {
        key: /Σ/,
        classNames: classNamesCommonOptimized,
        children: [
          {
            key: /мин/,
            classNames: classNamesOptimised,
            children: [
              {
                key: /.*/,
                classNames: classNamesOptimised,
                children: [],
              },
            ],
          },
          {
            key: /план/,
            classNames: classNamesNotOptimised,
            children: [
              {
                key: /.*/,
                classNames: classNamesNotOptimised,
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: classNamesOptimised,
            children: [
              {
                key: /.*/,
                classNames: classNamesOptimised,
                children: [],
              },
            ],
          },
        ],
      },
      {
        key: /^[^Σ]+$/,
        classNames: classNamesCommonOptimizedRgu,
        children: [
          {
            key: /мин/,
            classNames: classNamesOptimisedRgu,
            children: [
              {
                key: /.*/,
                classNames: classNamesOptimisedRgu,
                children: [],
              },
            ],
          },
          {
            key: /план/,
            classNames: classNamesNotOptimisedRgu,
            children: [
              {
                key: /.*/,
                classNames: classNamesNotOptimisedRgu,
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: classNamesOptimisedRgu,
            children: [
              {
                key: /.*/,
                classNames: classNamesOptimisedRgu,
                children: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    key: /Итог.ОГР/,
    classNames: 'plantLimit',
    children: [
      {
        key: /Σ/,
        classNames: 'plantLimit',
        children: [
          {
            key: /мин/,
            classNames: 'plantLimit',
            children: [
              {
                key: /.*/,
                classNames: 'plantLimit',
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: 'plantLimit',
            children: [
              {
                key: /.*/,
                classNames: 'plantLimit',
                children: [],
              },
            ],
          },
        ],
      },
      {
        key: /^[^Σ]+$/,
        classNames: 'plantLimitRgu',
        children: [
          {
            key: /мин/,
            classNames: 'plantLimitRgu',
            children: [
              {
                key: /.*/,
                classNames: 'plantLimitRgu',
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: 'plantLimitRgu',
            children: [
              {
                key: /.*/,
                classNames: 'plantLimitRgu',
                children: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    key: /Модес/,
    classNames: 'plantModes',
    children: [
      {
        key: /Σ/,
        classNames: 'plantModes',
        children: [
          {
            key: /мин/,
            classNames: 'plantModes',
            children: [
              {
                key: /.*/,
                classNames: 'plantModes',
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: 'plantModes',
            children: [
              {
                key: /.*/,
                classNames: 'plantModes',
                children: [],
              },
            ],
          },
          {
            key: /заяв/,
            classNames: 'plantModes',
            children: [
              {
                key: /.*/,
                classNames: 'plantModes',
                children: [],
              },
            ],
          },
        ],
      },
      {
        key: /^[^Σ]+$/,
        classNames: 'plantModesRgu',
        children: [
          {
            key: /мин/,
            classNames: 'plantModesRgu',
            children: [
              {
                key: /.*/,
                classNames: 'plantModesRgu',
                children: [],
              },
            ],
          },
          {
            key: /макс/,
            classNames: 'plantModesRgu',
            children: [
              {
                key: /.*/,
                classNames: 'plantModesRgu',
                children: [],
              },
            ],
          },
          {
            key: /заяв/,
            classNames: 'plantModesRgu',
            children: [
              {
                key: /.*/,
                classNames: 'plantModesRgu',
                children: [],
              },
            ],
          },
        ],
      },
    ],
  },
]

export const stationHeaderClassNameConfig: IHeaderClassesConfigItem[] = [
  {
    key: /.*/,
    classNames: classNamesValidator,
    children: [
      {
        key: /.*/,
        classNames: classNamesValidator,
        children: [
          {
            key: /.*/,
            classNames: classNamesValidator,
            children: [],
          },
        ],
      },
    ],
  },
  {
    key: /^Итог$/,
    classNames: classNamesCommonOptimized,
    children: [
      {
        key: /мин/,
        classNames: classNamesOptimised,
        children: [
          {
            key: /.*/,
            classNames: classNamesOptimised,
            children: [],
          },
        ],
      },
      {
        key: /план/,
        classNames: classNamesNotOptimised,
        children: [
          {
            key: /.*/,
            classNames: classNamesNotOptimised,
            children: [],
          },
        ],
      },
      {
        key: /макс/,
        classNames: classNamesOptimised,
        children: [
          {
            key: /.*/,
            classNames: classNamesOptimised,
            children: [],
          },
        ],
      },
    ],
  },
  {
    key: /Итог.ОГР/,
    classNames: 'plantLimit',
    children: [
      {
        key: /мин/,
        classNames: 'plantLimit',
        children: [
          {
            key: /.*/,
            classNames: 'plantLimit',
            children: [],
          },
        ],
      },
      {
        key: /макс/,
        classNames: 'plantLimit',
        children: [
          {
            key: /.*/,
            classNames: 'plantLimit',
            children: [],
          },
        ],
      },
    ],
  },
  {
    key: /Модес/,
    classNames: 'plantModes',
    children: [
      {
        key: /мин/,
        classNames: 'plantModes',
        children: [
          {
            key: /.*/,
            classNames: 'plantModes',
            children: [],
          },
        ],
      },
      {
        key: /макс/,
        classNames: 'plantModes',
        children: [
          {
            key: /.*/,
            classNames: 'plantModes',
            children: [],
          },
        ],
      },
      {
        key: /заяв/,
        classNames: 'plantModes',
        children: [
          {
            key: /.*/,
            classNames: 'plantModes',
            children: [],
          },
        ],
      },
    ],
  },
]
