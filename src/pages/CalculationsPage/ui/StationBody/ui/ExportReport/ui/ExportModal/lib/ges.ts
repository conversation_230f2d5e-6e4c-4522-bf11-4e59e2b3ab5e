// https://gitlab.ic.ntcees.ru/ups-ic/neptune/neptune-service/-/blob/develop/src/main/java/com/upsic/neptune/dto/enums/CalculationExportColumn.java?ref_type=heads
// SUMMARY=итог
// RESERVES=резерв
// RESTRICTIONS=Итог.ОГР
// CALC_MODEL=РМ
import { IExportModalGesColumn } from 'entities/pages/calcPage.entities.tsx'

export const gesColumns: IExportModalGesColumn[] = [
  {
    label: 'Итог',
    children: [
      {
        label: 'мин',
        value: 'RESULT_MIN',
        checked: true,
      },
      {
        label: 'план',
        value: 'P_GEN',
        checked: true,
      },
      {
        label: 'макс',
        value: 'RESULT_MAX',
        checked: true,
      },
    ],
  },
  {
    label: 'Резервы',
    children: [
      {
        label: 'резерв макс',
        value: 'RESERVES_MAX',
        checked: true,
      },
      {
        label: 'АВРЧМ',
        value: 'AVRCHM_LOAD',
        checked: true,
      },
      {
        label: 'НПРЧ',
        value: 'NPRCH',
        checked: true,
      },
    ],
  },
  {
    label: 'Итог.ОГР',
    children: [
      {
        label: 'мин',
        value: 'LIMIT_MIN',
        checked: true,
      },
      {
        label: 'макс',
        value: 'LIMIT_MAX',
        checked: true,
      },
    ],
  },
  {
    label: 'РМ',
    children: [
      {
        label: 'мин',
        value: 'CM_P_MIN',
        checked: true,
      },
      {
        label: 'макс',
        value: 'CM_P_MAX',
        checked: true,
      },
    ],
  },
  {
    label: 'Модес',
    children: [
      {
        label: 'мин',
        value: 'MODES_P_MIN',
        checked: true,
      },
      {
        label: 'макс',
        value: 'MODES_P_MAX',
        checked: true,
      },
      {
        label: 'заяв',
        value: 'MODES_DECLARED',
        checked: true,
      },
    ],
  },
  {
    label: 'ИСП',
    children: [
      {
        label: 'потр',
        value: 'CONSUMPT',
        checked: true,
      },
    ],
  },
]
