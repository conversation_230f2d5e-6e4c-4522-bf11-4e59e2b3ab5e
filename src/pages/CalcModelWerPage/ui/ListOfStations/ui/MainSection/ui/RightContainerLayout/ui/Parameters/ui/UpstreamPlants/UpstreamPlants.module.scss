.container {
  display: flex;
  width: 100%;
}

.title {
  color: var(--text-color);
  white-space: nowrap;
  width: var(--title-width);
}

.iconRedBtn {
  color: var(--red-color) !important;
  padding: 0 !important;
}

.addIcon {
  stroke: var(--primary-color);
  stroke-width: 0.5;
}

.actionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconCell {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.actionsWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;

  & > div:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -5px;
    width: 2px;
    height: 100%;
    background-color: #ccc;
  }
}

.table {
  width: calc(100% - var(--title-width));
  max-width: 940px;
}
